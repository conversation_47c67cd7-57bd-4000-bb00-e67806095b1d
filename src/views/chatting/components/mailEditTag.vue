<template>
  <el-dialog
    v-model="visible"
    :title="$t('text_edit_tag')"
    width="600px"
    :destroy-on-close="true"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form class="form" label-position="top" :model="form" ref="formRef" v-loading="loading">
      <el-form-item :label="$t('text_linked_game')" prop="projects">
        <el-select
          v-model="form.projects"
          :placeholder="$t('place_select')"
          multiple
          collapse-tags
          collapse-tags-tooltip
          clearable
          filterable
          :reserve-keyword="false"
        >
          <el-option
            v-for="(v, index) in gameList"
            :key="index"
            :label="v.app_name"
            :value="v.game_project"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('text_tags')" prop="label_id">
        <el-cascader
          v-model="form.label_id"
          style="width: 100%"
          :options="tagOpts"
          filterable
          :placeholder="form.projects.length > 0 ? $t('text_tag_placeholder') : '请先选择游戏'"
          :reserve-keyword="false"
          :props="{ multiple: true, emitPath: false, value: 'tag_id', label: 'tag_name' }"
          clearable
          :disabled="form.projects.length === 0"
        >
        </el-cascader>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ $t('btn_cancel') }}</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ $t('text_confirm') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { editTag, getTag, getTagList } from '@/api/overview';
import { useEnumStore } from '@/stores';
import type { FormInstance } from 'element-plus';
import { ElMessage } from 'element-plus';
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n();

interface Props {
  visible: boolean;
  emailId: string | number;
  projectName?: string;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  emailId: '',
  projectName: '',
});

const emit = defineEmits<{
  'update:visible': [value: boolean];
  success: [];
}>();

const visible = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val),
});

const loading = ref(false);
const formRef = ref<FormInstance>();
const gameList = computed(() => useEnumStore().gameList);
const tagOpts = ref([]);

const form = reactive({
  email_id: props.emailId,
  projects: [] as string[],
  label_id: [] as number[],
});

const handleClose = () => {
  visible.value = false;
  form.projects = [];
  form.label_id = [];
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  formRef.value.validate(async valid => {
    if (!valid || loading.value) return;

    loading.value = true;
    try {
      // 这里需要调用实际的API更新邮件标签
      await editTag({
        email_id: form.email_id,
        projects: form.projects,
        label_id: form.label_id,
      });

      ElMessage.success($t('text_success'));
      emit('success');
      handleClose();
    } catch (error) {
      console.error('更新标签失败:', error);
      ElMessage.error($t('text_operation_failed'));
    } finally {
      loading.value = false;
    }
  });
};

// 获取标签列表
const getTagOptions = async () => {
  try {
    if (form.projects.length > 0) {
      // 根据选择的游戏项目获取标签
      const promises = form.projects.map(project =>
        getTagList({
          project_name: project,
          lib_type: 1,
        })
      );

      const results = await Promise.all(promises);
      // 合并所有游戏的标签数据，去重
      const allTags = results.reduce((acc, res) => {
        if (res.data && Array.isArray(res.data)) {
          acc.push(...res.data);
        }
        return acc;
      }, []);

      // 根据tag_id去重
      const uniqueTags = allTags.filter(
        (tag, index, self) => index === self.findIndex(t => t.tag_id === tag.tag_id)
      );

      tagOpts.value = uniqueTags;
    } else {
      tagOpts.value = [];
    }
  } catch (error) {
    console.error('获取标签选项失败:', error);
    tagOpts.value = [];
  }
};

// 监听游戏选择变化
watch(
  () => form.projects,
  (newProjects, oldProjects) => {
    // 当游戏选择发生变化时，清空已选标签并重新获取标签选项
    if (JSON.stringify(newProjects) !== JSON.stringify(oldProjects)) {
      form.label_id = [];
      getTagOptions();
    }
  },
  { deep: true }
);

// 获取当前邮件的标签
const getCurrentTags = async () => {
  try {
    if (props.emailId) {
      const res = await getTag({
        email_id: props.emailId,
      });
      form.label_id = res.label_id || [];
      form.projects = res.projects || [];

      // 如果有游戏项目，则获取对应的标签选项
      if (form.projects.length > 0) {
        await getTagOptions();
      }
    }
  } catch (error) {
    console.error('获取当前标签失败:', error);
  }
};

onMounted(() => {
  form.email_id = props.emailId;
  getCurrentTags();
});
</script>

<style scoped>
.form {
  padding: 0;
}
</style>
